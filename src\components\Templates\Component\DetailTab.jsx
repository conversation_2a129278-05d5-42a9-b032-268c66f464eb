import { <PERSON><PERSON>, <PERSON>, <PERSON>, Divider, Form, Input, Row, Space } from "antd";
import React, { useState } from "react";

const { TextArea } = Input;

const DetailTab = ({ editData, formData, setFormData }) => {
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const handleSubmit = async (values) => {
    try {
      setSaving(true);
    } catch (error) {
      setSaving(false);
    }
  };
  const onCancel = () => {
    form.resetFields();
  };
  return (
    <>
      <Card
        title={
          <Space>
            <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
              Component Details
            </span>
          </Space>
        }
        className="tw-shadow-sm tw-border-0"
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "16px",
          },
          body: {
            paddingTop: "24px",
          },
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="tw-mt-6"
          size="large"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Template Name
              </span>
            }
            required
            rules={[
              {
                required: true,
                message: "Please enter a template name",
              },
              //   {
              //     min: 3,
              //     message:
              //       "Description must be at least 3 characters",
              //   },
            ]}
          >
            <Input
              placeholder="e.g., Business Website, Portfolio, Blog"
              className="tw-rounded-lg"
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
            />
          </Form.Item>
          <Form.Item
            name="description"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Description
              </span>
            }
            rules={
              [
                //   {
                //     required: true,
                //     message: "Please enter a Description",
                //   },
                //   {
                //     min: 3,
                //     message:
                //       "Description must be at least 3 characters",
                //   },
              ]
            }
          >
            <TextArea
              placeholder="Describe what this template is for..."
              rows={4}
              className="tw-rounded-lg"
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
            />
            {/* <Input
                          placeholder="e.g., Hero Section, Navigation Bar"
                          className="tw-rounded-lg"
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                        /> */}
          </Form.Item>

          <Divider className="tw-my-8" />
          <div className="tw-flex tw-justify-end tw-gap-4">
            <Button
              type="default"
              size="large"
              onClick={onCancel}
              className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={saving}
              className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              {saving
                ? "Saving..."
                : editData
                ? "Update Template"
                : "Save Template"}
            </Button>
          </div>
        </Form>
        {/* </Row> */}
      </Card>
    </>
  );
};

export default DetailTab;
