import { Collapse, Input, Select, Tooltip } from "antd";
import {
  ChevronDown,
  ChevronLeft,
  Eye,
  GripVertical,
  Loader2,
  Search,
} from "lucide-react";
import React, { useEffect } from "react";
import { generateGlobalPreviewHTML } from "../../Components/content";
import { useDrag } from "react-dnd";

const PageLibrary = ({
  isComponentOpen,
  setIsComponentOpen,
  isMobile,
  isTablet,
  type,
  listData = [],
  isLoading = false,
  onSearchChange,
  // LibraryItem,
}) => {
  // Draggable Component Library item - NO debouncing, immediate drag
  const LibraryItem = ({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: type,
        item: () => {
          console.log("Starting library component drag:", comp.name);
          return { component: comp };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            console.log("Library component dropped successfully:", comp.name);
          } else {
            console.log("Library component drag cancelled:", comp.name);
          }
        },
      }),
      [comp]
    );

    const previewContent = comp?.html_content ? (
      <div className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-p-1">
        <div
          className="tw-bg-white tw-rounded tw-shadow-sm tw-overflow-hidden"
          style={{
            width: "120px",
            height: "240px",
          }}
        >
          <iframe
            srcDoc={generateGlobalPreviewHTML({
              type: "component",
              data: [comp],
              title: "Component Preview",
            })}
            className="tw-border-0"
            title="Component Preview"
            style={{
              // width: "120px",
              height: "240px",
              pointerEvents: "none",
              overflow: "hidden",
              border: "none",
            }}
          />
        </div>
      </div>
    ) : (
      <div className="tw-flex tw-items-center tw-justify-center tw-h-20 tw-bg-white tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
        <div className="tw-text-center">
          <Eye className="tw-w-6 tw-h-6 tw-text-gray-400 tw-mx-auto tw-mb-1" />
          <p className="tw-text-xs tw-text-gray-500">No preview available</p>
        </div>
      </div>
    );

    return (
      <div className="tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-transition-colors">
        {/* Main component card */}
        <div
          ref={drag}
          className="tw-p-3 tw-pb-0 tw-cursor-move tw-select-none"
          style={{ opacity: isDragging ? 0.6 : 1 }}
        >
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-3">
            <div className="tw-flex-1">
              <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                {comp.name}
              </p>
              <p className="tw-text-xs tw-text-gray-500">
                {comp.placeholders ? comp.placeholders.length : 0} placeholders
              </p>
            </div>
            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
          </div>
          <div>
            <Select
              size="large"
              placeholder="V1"
              value={comp?.version || "v1"}
              onChange={(value) =>
                handleGlobalFieldChange("1", "version", value)
              }
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              className="tw-w-12 tw-h-3 tw-text-sm version-class"
              styles={{
                optionFontSize: "5px",
              }}
              style={{
                width: 54, // Adjust the width as needed
                height: 30,
                borderRadius: "100px", // Creates the pill shape
                fontSize: "12px",
              }}
              options={[
                { value: +"1", label: "v1" },
                { value: +"2", label: "v2" },
                { value: +"3", label: "v3" },
              ]}
            />
          </div>
        </div>

        {/* Ant Design Collapse for Preview */}
        <Collapse
          ghost
          size="small"
          className="tw-bg-transparent"
          expandIcon={({ isActive }) => (
            <ChevronDown
              className={`tw-w-3 tw-h-3 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                isActive ? "tw-rotate-180" : ""
              }`}
            />
          )}
          expandIconPosition="end"
          items={[
            {
              key: "preview",
              label: (
                <span className="tw-text-xs tw-text-gray-600">Preview</span>
              ),
              children: <div className="tw-p-0">{previewContent}</div>,
            },
          ]}
        />
      </div>
    );
  };
  return (
    <>
      <div
        className={`${
          isComponentOpen
            ? isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : isTablet
              ? "tw-w-64"
              : "tw-w-[18rem]"
            : "tw-w-0 tw-overflow-hidden"
        } tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isMobile && isComponentOpen ? "tw-shadow-2xl" : ""
        }`}
      >
        <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
            <Tooltip
              title={
                isComponentOpen ? "Hide Component List" : "Show Component List"
              }
            >
              <button
                onClick={() => setIsComponentOpen((v) => !v)}
                className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
              >
                <ChevronLeft
                  size={30}
                  className={` ${isComponentOpen ? "" : "tw-rotate-180 "}`}
                />
              </button>
            </Tooltip>
          </div>
          <div className="tw-p-3 md:tw-p-4 tw-border-gray-200">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div>
                <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-1">
                  Component Library
                </h3>
                <p className="tw-text-xs tw-text-gray-600 tw-hidden md:tw-block">
                  Drag components to the canvas to build your page
                </p>
              </div>
              {isMobile && (
                <button
                  onClick={() => setIsComponentOpen(false)}
                  className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg"
                >
                  <X className="tw-w-5 tw-h-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4">
          <Input
            placeholder="Search components..."
            size="middle"
            prefix={
              isLoading ? (
                <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
              ) : (
                <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
              )
            }
            // value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="tw-rounded-lg"
            allowClear
          />
          <Collapse
            ghost
            size="small"
            defaultActiveKey={Object.keys(listData)}
            className="tw-bg-transparent"
            expandIcon={({ isActive }) => (
              <ChevronDown
                className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                  isActive ? "tw-rotate-180" : ""
                }`}
              />
            )}
            expandIconPosition="end"
            items={Object.entries(listData).map(
              ([categoryId, categoryData]) => ({
                key: categoryId,
                label: (
                  <div className="tw-flex tw-items-center tw-text-center">
                    <div
                      className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                      style={{ backgroundColor: categoryData.color }}
                    />
                    <span className="tw-font-medium tw-text-gray-900 tw-text-base">
                      {categoryData.name}
                    </span>
                    <span className="tw-ml-2 tw-text-sm tw-text-gray-500">
                      ({categoryData.components.length})
                    </span>
                  </div>
                ),
                children: (
                  <div className="tw-space-y-2 tw-ml-2">
                    {categoryData.components.map((component) => (
                      <LibraryItem key={component.id} comp={component} />
                    ))}
                  </div>
                ),
              })
            )}
          />

          {Object.keys(listData).length === 0 && (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500">No components available</p>
              <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                Create components first to use them here
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageLibrary;
